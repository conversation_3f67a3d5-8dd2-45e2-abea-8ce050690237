{"event_id":"908cee15-8470-4cda-9b80-29cf9010869a","timestamp":"2025-06-08T20:24:43.968847","session_id":"109cc7be-760e-475d-a924-46732e0f13ee","event_type":"llm_query","llm_query":{"model":"Mistral Small 3.1 (OpenRouter)","prompt_length":1352,"temperature":null,"max_tokens":null,"has_system_prompt":false,"tools_available":0}}
{"event_id":"123824f6-d419-4a18-912c-3c93f6afc904","timestamp":"2025-06-08T20:24:55.688674","session_id":"109cc7be-760e-475d-a924-46732e0f13ee","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: HTTPSConnectionPool(host='openrouter.ai', port=443): Max retries exceeded with url: /api/v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001BD9C358F40>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))","has_stack_trace":true}}
{"event_id":"288f7d01-a768-42f0-b85b-2d557605c72a","timestamp":"2025-06-08T20:25:09.115641","session_id":"109cc7be-760e-475d-a924-46732e0f13ee","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: HTTPSConnectionPool(host='openrouter.ai', port=443): Max retries exceeded with url: /api/v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001BD9C359000>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))","has_stack_trace":true}}
{"event_id":"bc4704b9-61cb-414c-a345-f10dee1dc984","timestamp":"2025-06-08T20:25:15.122804","session_id":"109cc7be-760e-475d-a924-46732e0f13ee","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions","has_stack_trace":true}}
{"event_id":"0e5d665f-94ae-4e41-9f1f-9d800d7e8f5b","timestamp":"2025-06-08T20:25:24.738836","session_id":"109cc7be-760e-475d-a924-46732e0f13ee","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions","has_stack_trace":true}}
