"""
Main entry point for the Tic-Tac-Toe game.
Coordinates game logic and user interface.
"""

from game.logic import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ui.console import ConsoleUI


def main():
    """Main game loop."""
    game = TicTacToeGame()
    ui = ConsoleUI()
    
    ui.display_message("Welcome to Tic-Tac-Toe!")
    ui.display_message("Positions are numbered 1-9:")
    ui.display_message(" 1 | 2 | 3 ")
    ui.display_message(" 4 | 5 | 6 ")
    ui.display_message(" 7 | 8 | 9 ")
    
    while True:
        ui.display_board(game.board)
        
        # Get player move
        move = ui.get_player_move(game.current_player)
        
        # Validate and make move
        if not game.make_move(move):
            ui.display_message("Invalid move! Try again.")
            continue
        
        # Check for winner
        winner = game.check_winner()
        if winner:
            ui.display_board(game.board)
            ui.display_message(f"Player {winner} wins!")
            break
        
        # Check for draw
        if game.is_board_full():
            ui.display_board(game.board)
            ui.display_message("It's a draw!")
            break


if __name__ == "__main__":
    main()
