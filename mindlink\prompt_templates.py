"""
Extensible Prompt Templates for MindLink Intelligent Project Creation

This module provides customizable prompt templates that align with the MindLink project's
intelligent input comprehension patterns for optimal system understanding.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class PromptTemplate:
    """A structured prompt template for project creation."""
    name: str
    description: str
    system_prompt: str
    user_prompt_template: str
    example_projects: List[str]
    recommended_complexity: str
    technology_focus: List[str]


class PromptTemplateManager:
    """
    Manager for extensible prompt templates that leverage MindLink's
    intelligent comprehension capabilities.
    """
    
    def __init__(self):
        """Initialize with built-in templates."""
        self.templates = {}
        self._load_builtin_templates()
    
    def _load_builtin_templates(self):
        """Load built-in prompt templates for common project types."""
        
        # Web Application Template
        self.templates['web_app'] = PromptTemplate(
            name="Web Application",
            description="Full-stack web application with modern architecture",
            system_prompt="""You are an expert full-stack web developer and software architect. You excel at creating modern, scalable web applications with clean architecture, proper separation of concerns, and industry best practices. You understand modern frameworks, APIs, databases, and deployment strategies.""",
            user_prompt_template="""Create a {project_type} web application with {architectural_approach}.

PROJECT CONTEXT:
{project_description}

The system should include:
{core_features}

TECHNICAL REQUIREMENTS:
- Modern web technologies and frameworks
- RESTful API design
- Responsive user interface
- Database integration
- Authentication and authorization
- Error handling and logging
- Security best practices

QUALITY STANDARDS:
- Clean, maintainable code
- Proper documentation
- Comprehensive error handling
- Performance optimization
- Security considerations

Let the system intelligently determine the optimal file structure, naming conventions, and architectural patterns based on the project requirements. Create a professional, production-ready codebase.""",
            example_projects=[
                "E-commerce platform with user management and payment processing",
                "Social media dashboard with real-time updates",
                "Project management tool with team collaboration features"
            ],
            recommended_complexity="moderate to complex",
            technology_focus=["javascript", "python", "html", "css", "api", "database"]
        )
        
        # API Service Template
        self.templates['api_service'] = PromptTemplate(
            name="API Service",
            description="RESTful API service with microservices architecture",
            system_prompt="""You are an expert backend developer and API architect. You specialize in creating robust, scalable API services with proper authentication, validation, error handling, and documentation. You understand microservices, database design, and cloud deployment patterns.""",
            user_prompt_template="""Create a {project_type} API service with {architectural_approach}.

PROJECT CONTEXT:
{project_description}

The API should include:
{core_features}

TECHNICAL REQUIREMENTS:
- RESTful API endpoints
- Request/response validation
- Authentication and authorization
- Database models and migrations
- Error handling and logging
- API documentation
- Testing framework

ARCHITECTURE PATTERNS:
- Clean architecture principles
- Dependency injection
- Repository pattern
- Service layer abstraction

QUALITY STANDARDS:
- Comprehensive error handling
- Input validation and sanitization
- Security best practices
- Performance optimization
- Proper logging and monitoring

Design an intelligent, scalable API architecture that follows industry best practices.""",
            example_projects=[
                "User management API with OAuth integration",
                "E-commerce API with inventory and order management",
                "Content management API with file upload capabilities"
            ],
            recommended_complexity="moderate",
            technology_focus=["python", "api", "database", "authentication", "validation"]
        )
        
        # Data Science Template
        self.templates['data_science'] = PromptTemplate(
            name="Data Science Project",
            description="Machine learning and data analysis project",
            system_prompt="""You are an expert data scientist and machine learning engineer. You excel at creating comprehensive data science projects with proper data preprocessing, model development, evaluation, and deployment. You understand statistical analysis, feature engineering, and MLOps practices.""",
            user_prompt_template="""Create a {project_type} data science project with {architectural_approach}.

PROJECT CONTEXT:
{project_description}

The project should include:
{core_features}

TECHNICAL REQUIREMENTS:
- Data preprocessing and cleaning
- Exploratory data analysis
- Feature engineering
- Model development and training
- Model evaluation and validation
- Visualization and reporting
- Model deployment pipeline

METHODOLOGY:
- Scientific approach to problem solving
- Reproducible research practices
- Version control for data and models
- Comprehensive documentation

QUALITY STANDARDS:
- Statistical rigor
- Proper validation techniques
- Clear visualizations
- Interpretable results
- Production-ready code

Create an intelligent data science workflow that follows best practices for reproducible research and model deployment.""",
            example_projects=[
                "Customer churn prediction with ensemble methods",
                "Natural language processing for sentiment analysis",
                "Computer vision for image classification"
            ],
            recommended_complexity="complex",
            technology_focus=["python", "machine_learning", "data_analysis", "visualization"]
        )
        
        # Game Development Template
        self.templates['game_dev'] = PromptTemplate(
            name="Game Development",
            description="Interactive game with modern game development patterns",
            system_prompt="""You are an expert game developer and software architect. You excel at creating engaging, well-structured games with proper game loops, state management, and performance optimization. You understand game design patterns, graphics programming, and user experience.""",
            user_prompt_template="""Create a {project_type} game with {architectural_approach}.

PROJECT CONTEXT:
{project_description}

The game should include:
{core_features}

TECHNICAL REQUIREMENTS:
- Game loop and state management
- Input handling and controls
- Graphics and rendering
- Audio integration
- Game mechanics and logic
- User interface and menus
- Save/load functionality

GAME DESIGN PATTERNS:
- Entity-Component-System (ECS)
- State machines
- Observer pattern
- Command pattern

QUALITY STANDARDS:
- Smooth performance
- Responsive controls
- Engaging gameplay
- Clean code architecture
- Extensible design

Design an intelligent game architecture that provides engaging gameplay while maintaining clean, maintainable code.""",
            example_projects=[
                "2D platformer with physics and collectibles",
                "Turn-based strategy game with AI opponents",
                "Puzzle game with progressive difficulty"
            ],
            recommended_complexity="moderate to complex",
            technology_focus=["python", "game_development", "graphics", "user_interface"]
        )
        
        # Automation Tool Template
        self.templates['automation'] = PromptTemplate(
            name="Automation Tool",
            description="Automation and scripting solution for workflow optimization",
            system_prompt="""You are an expert automation engineer and DevOps specialist. You excel at creating efficient automation tools that streamline workflows, integrate with various systems, and provide reliable, maintainable solutions. You understand scripting, APIs, and system integration.""",
            user_prompt_template="""Create a {project_type} automation tool with {architectural_approach}.

PROJECT CONTEXT:
{project_description}

The automation should include:
{core_features}

TECHNICAL REQUIREMENTS:
- Workflow automation and orchestration
- System integration and APIs
- Configuration management
- Error handling and recovery
- Logging and monitoring
- Scheduling and triggers
- User interface or CLI

AUTOMATION PATTERNS:
- Pipeline architecture
- Event-driven processing
- Retry mechanisms
- Graceful degradation

QUALITY STANDARDS:
- Reliability and robustness
- Comprehensive error handling
- Clear logging and reporting
- Easy configuration and maintenance
- Scalable design

Create an intelligent automation solution that improves efficiency while being reliable and maintainable.""",
            example_projects=[
                "CI/CD pipeline automation with testing and deployment",
                "Data processing pipeline with multiple sources",
                "System monitoring and alerting automation"
            ],
            recommended_complexity="moderate",
            technology_focus=["python", "automation", "scripting", "integration"]
        )
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """Get a specific template by name."""
        return self.templates.get(template_name)
    
    def list_templates(self) -> List[str]:
        """List all available template names."""
        return list(self.templates.keys())
    
    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific template."""
        template = self.templates.get(template_name)
        if template:
            return {
                'name': template.name,
                'description': template.description,
                'example_projects': template.example_projects,
                'recommended_complexity': template.recommended_complexity,
                'technology_focus': template.technology_focus
            }
        return None
    
    def add_custom_template(self, template: PromptTemplate):
        """Add a custom template to the manager."""
        self.templates[template.name.lower().replace(' ', '_')] = template
    
    def generate_prompt(self, template_name: str, **kwargs) -> Optional[str]:
        """
        Generate a complete prompt using a template with provided parameters.
        
        Args:
            template_name: Name of the template to use
            **kwargs: Parameters to fill in the template
            
        Returns:
            Generated prompt string or None if template not found
        """
        template = self.templates.get(template_name)
        if not template:
            return None
        
        try:
            return template.user_prompt_template.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Missing required parameter for template '{template_name}': {e}")


# Global instance for easy access
prompt_manager = PromptTemplateManager()


def get_intelligent_prompt_for_project(project_description: str, 
                                     project_type: str = None,
                                     architectural_approach: str = None,
                                     core_features: str = None) -> str:
    """
    Generate an intelligent prompt for project creation based on description.
    
    This function analyzes the project description and selects the most appropriate
    template, then generates a comprehensive prompt for the MindLink system.
    
    Args:
        project_description: Description of the project to create
        project_type: Optional specific project type
        architectural_approach: Optional architectural approach
        core_features: Optional core features description
        
    Returns:
        Intelligent prompt optimized for MindLink comprehension
    """
    # Analyze project description to determine best template
    description_lower = project_description.lower()
    
    # Template selection logic
    if any(word in description_lower for word in ['web', 'website', 'frontend', 'backend', 'full-stack']):
        template_name = 'web_app'
    elif any(word in description_lower for word in ['api', 'service', 'microservice', 'endpoint']):
        template_name = 'api_service'
    elif any(word in description_lower for word in ['data', 'machine learning', 'ml', 'analysis', 'model']):
        template_name = 'data_science'
    elif any(word in description_lower for word in ['game', 'gaming', 'player', 'level']):
        template_name = 'game_dev'
    elif any(word in description_lower for word in ['automation', 'script', 'workflow', 'pipeline']):
        template_name = 'automation'
    else:
        # Default to web app for general projects
        template_name = 'web_app'
    
    # Set defaults if not provided
    if not project_type:
        project_type = prompt_manager.get_template(template_name).name.lower()
    if not architectural_approach:
        architectural_approach = "modern, scalable architecture"
    if not core_features:
        core_features = "Core functionality as determined by intelligent analysis"
    
    # Generate the prompt
    return prompt_manager.generate_prompt(
        template_name,
        project_type=project_type,
        project_description=project_description,
        architectural_approach=architectural_approach,
        core_features=core_features
    )
