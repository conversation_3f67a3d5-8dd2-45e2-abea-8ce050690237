"""
Console-based user interface for Tic-Tac-Toe.
Handles display and user input through terminal.
"""


class ConsoleUI:
    """Console interface for the Tic-Tac-Toe game."""
    
    def display_board(self, board):
        """Display the current game board."""
        print("\n   |   |   ")
        print(f" {board[0]} | {board[1]} | {board[2]} ")
        print("___|___|___")
        print("   |   |   ")
        print(f" {board[3]} | {board[4]} | {board[5]} ")
        print("___|___|___")
        print("   |   |   ")
        print(f" {board[6]} | {board[7]} | {board[8]} ")
        print("   |   |   \n")
    
    def get_player_move(self, current_player):
        """Get move input from the current player."""
        while True:
            try:
                move = int(input(f"Player {current_player}, enter position (1-9): ")) - 1
                return move
            except ValueError:
                print("Please enter a valid number between 1 and 9.")
    
    def display_message(self, message):
        """Display a message to the console."""
        print(message)
