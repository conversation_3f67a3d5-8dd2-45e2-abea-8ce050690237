"""
Test script for the new intelligent file detection and creation system.

This script tests the LLM-based project analysis and intelligent file creation
without the old regex-based limitations.
"""

import sys
import os

# Add the mindlink directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mindlink'))

from mindlink.intelligent_file_analyzer import IntelligentFileAnalyzer
from mindlink.prompt_templates import get_intelligent_prompt_for_project, prompt_manager
from mindlink.models.openrouter import OpenRouterModel


def test_prompt_templates():
    """Test the prompt template system."""
    print("=== Testing Prompt Templates ===")
    
    # List available templates
    templates = prompt_manager.list_templates()
    print(f"Available templates: {templates}")
    
    # Test intelligent prompt generation
    project_description = "Create a modern e-commerce platform with user authentication, product catalog, shopping cart, and payment processing"
    
    intelligent_prompt = get_intelligent_prompt_for_project(
        project_description=project_description,
        architectural_approach="microservices architecture with clean separation of concerns",
        core_features="User management, product catalog, shopping cart, payment processing, order management"
    )
    
    print(f"\nGenerated intelligent prompt:\n{intelligent_prompt[:500]}...")
    print("\n" + "="*50)


def test_file_analyzer():
    """Test the intelligent file analyzer (requires API key)."""
    print("=== Testing Intelligent File Analyzer ===")
    
    # Check if we have an API key
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("No OpenRouter API key found. Skipping LLM-based tests.")
        print("Set OPENROUTER_API_KEY environment variable to test LLM features.")
        return
    
    try:
        # Create LLM instance
        llm = OpenRouterModel(
            api_key=api_key,
            model_name="mistral-small-3.1",
            temperature=0.3,
            max_tokens=2048
        )
        
        # Create file analyzer
        analyzer = IntelligentFileAnalyzer(llm)
        
        # Test project analysis
        test_requests = [
            "Create a Python web application for managing a library with book catalog, user accounts, and borrowing system",
            "Build a data science project to analyze customer behavior and predict churn using machine learning",
            "Develop a simple 2D game with player movement, enemies, and scoring system",
            "Create an automation tool to process CSV files and generate reports"
        ]
        
        for i, request in enumerate(test_requests, 1):
            print(f"\nTest {i}: {request}")
            print("-" * 60)
            
            analysis = analyzer.analyze_project_request(request)
            
            if analysis:
                print(f"Multi-file project: {analysis.is_multi_file}")
                print(f"Estimated files: {analysis.estimated_files}")
                print(f"Project type: {analysis.project_type}")
                print(f"Complexity: {analysis.complexity_level}")
                print(f"Architecture: {analysis.recommended_architecture}")
                print(f"Tech stack: {', '.join(analysis.technology_stack)}")
                print(f"Confidence: {analysis.confidence_score}")
                
                if analysis.file_structure:
                    print("Proposed file structure:")
                    for j, file_info in enumerate(analysis.file_structure[:3], 1):  # Show first 3 files
                        print(f"  {j}. {file_info.get('filename', 'N/A')} - {file_info.get('purpose', 'N/A')}")
                    if len(analysis.file_structure) > 3:
                        print(f"  ... and {len(analysis.file_structure) - 3} more files")
            else:
                print("Analysis failed or returned None")
            
            print()
    
    except Exception as e:
        print(f"Error testing file analyzer: {e}")
        print("This might be due to API key issues or network connectivity.")
    
    print("="*50)


def test_intelligent_naming():
    """Test intelligent filename generation."""
    print("=== Testing Intelligent Filename Generation ===")
    
    # Test context for filename generation
    test_contexts = [
        {
            'original_goal': 'Create a web application for task management',
            'file_description_base': 'web application component',
            'files_created_count': 0,
            'num_files_remaining': 5
        },
        {
            'original_goal': 'Build a machine learning model for image classification',
            'file_description_base': 'ML project module',
            'files_created_count': 1,
            'num_files_remaining': 3
        }
    ]
    
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("No OpenRouter API key found. Using fallback naming only.")
        return
    
    try:
        llm = OpenRouterModel(
            api_key=api_key,
            model_name="mistral-small-3.1",
            temperature=0.4,
            max_tokens=100
        )
        
        analyzer = IntelligentFileAnalyzer(llm)
        
        for i, context in enumerate(test_contexts, 1):
            print(f"\nTest Context {i}: {context['original_goal']}")
            print("-" * 40)
            
            for file_num in range(1, 4):  # Test first 3 files
                filename = analyzer.generate_intelligent_filename(
                    context, file_num, f"Component {file_num} for the project"
                )
                print(f"  File {file_num}: {filename}")
    
    except Exception as e:
        print(f"Error testing intelligent naming: {e}")
    
    print("="*50)


def main():
    """Run all tests."""
    print("Testing MindLink Intelligent File Detection and Creation System")
    print("="*70)
    
    # Test prompt templates (no API key required)
    test_prompt_templates()
    
    # Test file analyzer (requires API key)
    test_file_analyzer()
    
    # Test intelligent naming (requires API key)
    test_intelligent_naming()
    
    print("\nTesting completed!")
    print("\nKey improvements in the new system:")
    print("✓ No rigid regex patterns - full natural language understanding")
    print("✓ No artificial file count limits - LLM determines appropriate scope")
    print("✓ Intelligent, contextual file naming")
    print("✓ Creative architectural decision-making")
    print("✓ Extensible prompt templates for different project types")
    print("✓ Full LLM delegation for project structure and naming")


if __name__ == "__main__":
    main()
