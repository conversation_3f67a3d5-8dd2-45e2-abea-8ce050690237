# Tic-Tac-Toe Game

A simple console-based Tic-Tac-Toe game implemented in Python.

## Features

- Two-player gameplay (X and O)
- Console-based interface
- Input validation and error handling
- Win detection and draw detection

## How to Run

```bash
python main.py
```

## How to Play

Players take turns entering positions 1-9 to place their marks on the board. The first player to get three marks in a row (horizontally, vertically, or diagonally) wins!
