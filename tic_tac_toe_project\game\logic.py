"""
Core game logic for Tic-Tac-Toe.
Handles board state, move validation, and win detection.
"""


class TicTacToeGame:
    """Main game logic class for Tic-Tac-Toe."""
    
    def __init__(self):
        """Initialize a new game with empty board."""
        self.board = [' ' for _ in range(9)]
        self.current_player = 'X'
    
    def make_move(self, position):
        """Make a move at the given position (0-8)."""
        if self.is_valid_move(position):
            self.board[position] = self.current_player
            self.switch_player()
            return True
        return False
    
    def is_valid_move(self, position):
        """Check if a move is valid."""
        return 0 <= position <= 8 and self.board[position] == ' '
    
    def switch_player(self):
        """Switch between X and O players."""
        self.current_player = 'O' if self.current_player == 'X' else 'X'
    
    def check_winner(self):
        """Check if there's a winner. Returns 'X', 'O', or None."""
        winning_combinations = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],  # rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8],  # columns
            [0, 4, 8], [2, 4, 6]              # diagonals
        ]
        
        for combo in winning_combinations:
            if (self.board[combo[0]] == self.board[combo[1]] == 
                self.board[combo[2]] != ' '):
                return self.board[combo[0]]
        return None
    
    def is_board_full(self):
        """Check if the board is full (draw condition)."""
        return ' ' not in self.board
    
    def reset_game(self):
        """Reset the game to initial state."""
        self.board = [' ' for _ in range(9)]
        self.current_player = 'X'
